package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.infrastructure.persistence.entity.OrderJpaEntity;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-09T21:58:43+0700",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OrderJpaMapperImpl implements OrderJpaMapper {

    @Override
    public Order toDomain(OrderJpaEntity entity) {
        if ( entity == null ) {
            return null;
        }

        Order.OrderBuilder order = Order.builder();

        order.orderId( stringToOrderId( entity.getOrderId() ) );
        order.memberId( entity.getMemberId() );
        order.contractId( entity.getContractId() );
        order.symbol( stringToSymbol( entity.getSymbol() ) );
        order.coinSymbol( entity.getCoinSymbol() );
        order.baseSymbol( entity.getBaseSymbol() );
        order.direction( entity.getDirection() );
        order.type( entity.getType() );
        order.price( bigDecimalToMoney( entity.getPrice() ) );
        order.triggerPrice( bigDecimalToMoney( entity.getTriggerPrice() ) );
        order.triggerType( entity.getTriggerType() );
        order.volume( entity.getVolume() );
        order.dealVolume( entity.getDealVolume() );
        order.dealMoney( bigDecimalToMoney( entity.getDealMoney() ) );
        order.fee( bigDecimalToMoney( entity.getFee() ) );
        order.status( entity.getStatus() );
        order.createTime( entity.getCreateTime() );
        order.completeTime( entity.getCompleteTime() );
        order.canceledTime( entity.getCanceledTime() );
        order.executeTime( entity.getExecuteTime() );
        order.expireTime( entity.getExpireTime() );
        order.timeInForce( entity.getTimeInForce() );
        order.leverage( entity.getLeverage() );
        order.reduceOnly( entity.getReduceOnly() );
        order.liquidation( entity.getLiquidation() );
        order.adl( entity.getAdl() );
        order.implied( entity.getImplied() );
        order.sourceOrderId( entity.getSourceOrderId() );
        order.ocoId( entity.getOcoId() );
        order.ocoOrderNo( entity.getOcoOrderNo() );
        order.callbackRate( entity.getCallbackRate() );
        order.activationPrice( bigDecimalToMoney( entity.getActivationPrice() ) );
        order.postOnly( entity.getPostOnly() );
        order.cancelReason( entity.getCancelReason() );
        order.maxSlippage( entity.getMaxSlippage() );
        order.fillOrKill( entity.getFillOrKill() );
        order.immediateOrCancel( entity.getImmediateOrCancel() );
        order.selfTradePreventionMode( entity.getSelfTradePreventionMode() );

        return order.build();
    }

    @Override
    public OrderJpaEntity toEntity(Order domain) {
        if ( domain == null ) {
            return null;
        }

        OrderJpaEntity.OrderJpaEntityBuilder orderJpaEntity = OrderJpaEntity.builder();

        orderJpaEntity.orderId( orderIdToString( domain.getOrderId() ) );
        orderJpaEntity.memberId( domain.getMemberId() );
        orderJpaEntity.contractId( domain.getContractId() );
        orderJpaEntity.symbol( symbolToString( domain.getSymbol() ) );
        orderJpaEntity.coinSymbol( domain.getCoinSymbol() );
        orderJpaEntity.baseSymbol( domain.getBaseSymbol() );
        orderJpaEntity.direction( domain.getDirection() );
        orderJpaEntity.type( domain.getType() );
        orderJpaEntity.price( moneyToBigDecimal( domain.getPrice() ) );
        orderJpaEntity.triggerPrice( moneyToBigDecimal( domain.getTriggerPrice() ) );
        orderJpaEntity.triggerType( domain.getTriggerType() );
        orderJpaEntity.volume( domain.getVolume() );
        orderJpaEntity.dealVolume( domain.getDealVolume() );
        orderJpaEntity.dealMoney( moneyToBigDecimal( domain.getDealMoney() ) );
        orderJpaEntity.fee( moneyToBigDecimal( domain.getFee() ) );
        orderJpaEntity.status( domain.getStatus() );
        orderJpaEntity.createTime( domain.getCreateTime() );
        orderJpaEntity.completeTime( domain.getCompleteTime() );
        orderJpaEntity.canceledTime( domain.getCanceledTime() );
        orderJpaEntity.executeTime( domain.getExecuteTime() );
        orderJpaEntity.expireTime( domain.getExpireTime() );
        orderJpaEntity.timeInForce( domain.getTimeInForce() );
        orderJpaEntity.leverage( domain.getLeverage() );
        orderJpaEntity.reduceOnly( domain.getReduceOnly() );
        orderJpaEntity.liquidation( domain.getLiquidation() );
        orderJpaEntity.adl( domain.getAdl() );
        orderJpaEntity.implied( domain.getImplied() );
        orderJpaEntity.sourceOrderId( domain.getSourceOrderId() );
        orderJpaEntity.ocoId( domain.getOcoId() );
        orderJpaEntity.ocoOrderNo( domain.getOcoOrderNo() );
        orderJpaEntity.callbackRate( domain.getCallbackRate() );
        orderJpaEntity.activationPrice( moneyToBigDecimal( domain.getActivationPrice() ) );
        orderJpaEntity.postOnly( domain.getPostOnly() );
        orderJpaEntity.cancelReason( domain.getCancelReason() );
        orderJpaEntity.maxSlippage( domain.getMaxSlippage() );
        orderJpaEntity.fillOrKill( domain.getFillOrKill() );
        orderJpaEntity.immediateOrCancel( domain.getImmediateOrCancel() );
        orderJpaEntity.selfTradePreventionMode( domain.getSelfTradePreventionMode() );

        return orderJpaEntity.build();
    }
}
