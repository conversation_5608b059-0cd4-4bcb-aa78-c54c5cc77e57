<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="652586bf-43f5-4371-8e8e-98f82d07086b" name="Changes" comment="update MongoDB configuration in application-dev.yaml and simplify configmap creation in update.sh">
      <change beforePath="$PROJECT_DIR$/environments/dev/test/external-api/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/environments/dev/test/external-api/application.yaml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;edwardnguyen98&quot;,
      &quot;fullname&quot;: &quot;edwardnguyen&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://gitlab.com/trading-platform7362712/terraform-deploy.git&quot;,
    &quot;second&quot;: &quot;3ac13dfb-049e-45f6-85ed-3cb4259e8e14&quot;
  }
}</component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2zHJHdHXPIzN1oLH687JCMNVe57" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SONARLINT_PRECOMMIT_ANALYSIS&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Project/cex-be/bitcello-deployment&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26574.91" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26574.91" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="652586bf-43f5-4371-8e8e-98f82d07086b" name="Changes" comment="" />
      <created>1751381978884</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751381978884</updated>
      <workItem from="1751381980144" duration="1386000" />
      <workItem from="1751385522026" duration="763000" />
      <workItem from="1751386782065" duration="1212000" />
      <workItem from="1751470011162" duration="30000" />
      <workItem from="1751477233959" duration="10000" />
      <workItem from="1751477812136" duration="286000" />
      <workItem from="1751795258814" duration="3000" />
      <workItem from="1751986133843" duration="1253000" />
    </task>
    <task id="LOCAL-00001" summary="update MongoDB configuration in application-dev.yaml and simplify configmap creation in update.sh">
      <option name="closed" value="true" />
      <created>1751387062159</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751387062159</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="update MongoDB configuration in application-dev.yaml and simplify configmap creation in update.sh" />
    <option name="LAST_COMMIT_MESSAGE" value="update MongoDB configuration in application-dev.yaml and simplify configmap creation in update.sh" />
  </component>
</project>