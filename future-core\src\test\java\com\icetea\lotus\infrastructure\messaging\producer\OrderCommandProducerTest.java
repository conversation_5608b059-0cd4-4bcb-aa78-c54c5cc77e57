package com.icetea.lotus.infrastructure.messaging.producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.exception.MessagingException;
import com.icetea.lotus.core.domain.value.OrderId;
import com.icetea.lotus.core.domain.value.Symbol;
import com.icetea.lotus.infrastructure.messaging.command.OrderCommand;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Test cho OrderCommandProducer
 * 
 * <AUTHOR> nguyen
 */
@ExtendWith(MockitoExtension.class)
class OrderCommandProducerTest {

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @Mock
    private ObjectMapper objectMapper;

    private OrderCommandProducer orderCommandProducer;

    private final String orderCommandsTopic = "order-commands";

    @BeforeEach
    void setUp() {
        orderCommandProducer = new OrderCommandProducer(kafkaTemplate, objectMapper);
        // Set topic value using reflection or create a constructor that accepts it
        try {
            var field = OrderCommandProducer.class.getDeclaredField("orderCommandsTopic");
            field.setAccessible(true);
            field.set(orderCommandProducer, orderCommandsTopic);
        } catch (Exception e) {
            fail("Failed to set orderCommandsTopic field");
        }
    }

    @Test
    void sendPlaceOrderCommand_Success() throws Exception {
        // Given
        Order order = Order.builder()
                .orderId(OrderId.of("ORD123"))
                .symbol(Symbol.of("BTCUSDT"))
                .build();

        String commandJson = "{\"commandId\":\"test\",\"type\":\"PLACE_ORDER\"}";
        when(objectMapper.writeValueAsString(any(OrderCommand.class))).thenReturn(commandJson);

        // When
        orderCommandProducer.sendPlaceOrderCommand(order);

        // Then
        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> keyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> valueCaptor = ArgumentCaptor.forClass(String.class);

        verify(kafkaTemplate).send(topicCaptor.capture(), keyCaptor.capture(), valueCaptor.capture());
        
        assertEquals(orderCommandsTopic, topicCaptor.getValue());
        assertEquals("BTCUSDT", keyCaptor.getValue());
        assertEquals(commandJson, valueCaptor.getValue());
    }

    @Test
    void sendPlaceOrderCommand_JsonProcessingException() throws Exception {
        // Given
        Order order = Order.builder()
                .orderId(OrderId.of("ORD123"))
                .symbol(Symbol.of("BTCUSDT"))
                .build();

        when(objectMapper.writeValueAsString(any(OrderCommand.class)))
                .thenThrow(new RuntimeException("JSON processing failed"));

        // When & Then
        MessagingException exception = assertThrows(MessagingException.class, () -> {
            orderCommandProducer.sendPlaceOrderCommand(order);
        });

        assertEquals("Failed to send order command to Kafka", exception.getMessage());
        assertTrue(exception.getCause() instanceof RuntimeException);
    }

    @Test
    void sendPlaceOrderCommand_KafkaException() throws Exception {
        // Given
        Order order = Order.builder()
                .orderId(OrderId.of("ORD123"))
                .symbol(Symbol.of("BTCUSDT"))
                .build();

        String commandJson = "{\"commandId\":\"test\",\"type\":\"PLACE_ORDER\"}";
        when(objectMapper.writeValueAsString(any(OrderCommand.class))).thenReturn(commandJson);
        when(kafkaTemplate.send(eq(orderCommandsTopic), eq("BTCUSDT"), eq(commandJson)))
                .thenThrow(new RuntimeException("Kafka send failed"));

        // When & Then
        MessagingException exception = assertThrows(MessagingException.class, () -> {
            orderCommandProducer.sendPlaceOrderCommand(order);
        });

        assertEquals("Failed to send order command to Kafka", exception.getMessage());
        assertTrue(exception.getCause() instanceof RuntimeException);
    }
}
