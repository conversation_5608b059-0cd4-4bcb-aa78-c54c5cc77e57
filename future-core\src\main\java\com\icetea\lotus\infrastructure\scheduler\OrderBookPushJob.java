package com.icetea.lotus.infrastructure.scheduler;

import com.icetea.lotus.application.port.input.ManageContractUseCase;
import com.icetea.lotus.core.domain.entity.OrderBook;
import com.icetea.lotus.core.domain.service.OrderMatchingEngineService;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.cache.RedisDataService;
import com.icetea.lotus.infrastructure.event.OrderBookChangedEvent;
import com.icetea.lotus.infrastructure.websocket.OrderBookHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Job để cập nhật OrderBook định kỳ
 */
@Component
@Slf4j
public class OrderBookPushJob {

    private final OrderMatchingEngineService orderMatchingEngineService;
    private final ManageContractUseCase manageContractUseCase;
    private final OrderBookHandler orderBookHandler;
    private final TaskExecutor taskExecutor;
    private final RedisDataService redisDataService;
    private final ApplicationEventPublisher eventPublisher;

    // Map để lưu trữ OrderBook cuối cùng cho mỗi symbol
    private final Map<String, OrderBook> lastOrderBooks = new ConcurrentHashMap<>();

    @Autowired
    public OrderBookPushJob(
            OrderMatchingEngineService orderMatchingEngineService,
            ManageContractUseCase manageContractUseCase,
            OrderBookHandler orderBookHandler,
            RedisDataService redisDataService,
            ApplicationEventPublisher eventPublisher) {
        this.orderMatchingEngineService = orderMatchingEngineService;
        this.manageContractUseCase = manageContractUseCase;
        this.orderBookHandler = orderBookHandler;
        this.redisDataService = redisDataService;
        this.eventPublisher = eventPublisher;

        // Tạo một TaskExecutor mới thay vì yêu cầu bean scheduledTaskExecutor
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(50);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("orderbook-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        this.taskExecutor = executor;

        log.info("Đã tạo TaskExecutor cho OrderBookPushJob với corePoolSize = 5, maxPoolSize = 10, queueCapacity = 50");
    }

    /**
     * Cập nhật OrderBook mỗi giây
     */
    @Scheduled(fixedRate = 200)
    public void pushOrderBook() {
//        log.info("Cập nhật OrderBook định kỳ");

        List<String> symbols;
        try {
            symbols = (List<String>) redisDataService.get("list-symbol-push-order-book");
        } catch (ClassCastException e) {
            log.warn("ClassCastException khi đọc symbols từ Redis, sẽ reload từ database", e);
            symbols = null;
        }

//        log.info("pushOrderBook symbols from redis: {}", symbols);
        if(symbols == null || symbols.isEmpty()) {
            // Lấy danh sách symbol
            symbols = manageContractUseCase.findAllEnabled().stream()
                    .map(contract -> contract.getSymbol()) // ContractDto.getSymbol() trả về String
                    .collect(java.util.stream.Collectors.toList());
//            log.info("pushOrderBook symbols from database: {}", symbols);
            redisDataService.set("list-symbol-push-order-book", symbols);
        }

        // Cập nhật OrderBook cho từng symbol
        for (String symbolStr : symbols) {
//            log.info("LOOP pushOrderBook symbolStr: {}", symbolStr);
            Symbol symbol = Symbol.of(symbolStr);

            // Sử dụng TaskExecutor để cập nhật OrderBook bất đồng bộ
            taskExecutor.execute(() -> {
                try {
                    // Lấy OrderBook
                    OrderBook orderBook = orderMatchingEngineService.getOrderBook(symbol);

                    if (orderBook != null) {
                        // Kiểm tra xem OrderBook có thay đổi đáng kể không
                        boolean hasSignificantChange = hasSignificantChange(symbolStr, orderBook);

                        if (hasSignificantChange) {
                            log.info("pushOrderBook getOrderBook symbol = {}, orderBook = {}", symbolStr, orderBook);

                            // Gửi OrderBook qua WebSocket
                            orderBookHandler.handleOrderBook(symbolStr, orderBook);

                            // Phát ra sự kiện OrderBookChangedEvent
                            eventPublisher.publishEvent(new OrderBookChangedEvent(
                                    this,
                                    symbol,
                                    orderBook,
                                    true));

                            // Cập nhật OrderBook cuối cùng
                            lastOrderBooks.put(symbolStr, orderBook);
                        } else {
                            log.debug("Không có thay đổi đáng kể cho OrderBook, symbol = {}", symbolStr);
                        }
                    } else {
                        log.debug("OrderBook null cho symbol = {}, có thể contract chưa được khởi tạo", symbolStr);
                    }
                } catch (IllegalArgumentException e) {
                    log.warn("Contract không tồn tại hoặc không được kích hoạt cho symbol = {}: {}", symbolStr, e.getMessage());
                } catch (Exception e) {
                    log.error("Lỗi khi cập nhật OrderBook, symbol = {}", symbolStr, e);
                }
            });
        }
    }

    /**
     * Kiểm tra xem OrderBook có thay đổi đáng kể không
     * @param symbolStr Symbol của hợp đồng
     * @param orderBook OrderBook mới
     * @return true nếu có thay đổi đáng kể, false nếu không
     */
    private boolean hasSignificantChange(String symbolStr, OrderBook orderBook) {
        // Lấy OrderBook cuối cùng
        OrderBook lastOrderBook = lastOrderBooks.get(symbolStr);

        // Nếu không có OrderBook cuối cùng, coi như có thay đổi đáng kể
        if (lastOrderBook == null) {
            return true;
        }

        // Kiểm tra thay đổi giá tốt nhất
        if (hasBestPriceChanged(lastOrderBook, orderBook)) {
            return true;
        }

        // Kiểm tra thay đổi giá giao dịch cuối cùng
        if (hasLastPriceChanged(lastOrderBook, orderBook)) {
            return true;
        }

        // Kiểm tra thay đổi giá đánh dấu
        return hasMarkPriceChanged(lastOrderBook, orderBook);
    }

    /**
     * Kiểm tra xem giá tốt nhất có thay đổi không
     * @param lastOrderBook OrderBook cuối cùng
     * @param newOrderBook OrderBook mới
     * @return true nếu có thay đổi, false nếu không
     */
    private boolean hasBestPriceChanged(OrderBook lastOrderBook, OrderBook newOrderBook) {
        // Kiểm tra giá mua tốt nhất
        if (!lastOrderBook.getBids().isEmpty() && !newOrderBook.getBids().isEmpty()) {
            Money lastBestBid = lastOrderBook.getBids().get(0).getPrice();
            Money newBestBid = newOrderBook.getBids().get(0).getPrice();

            if (!lastBestBid.equals(newBestBid)) {
                return true;
            }
        } else if (lastOrderBook.getBids().isEmpty() != newOrderBook.getBids().isEmpty()) {
            return true;
        }

        // Kiểm tra giá bán tốt nhất
        if (!lastOrderBook.getAsks().isEmpty() && !newOrderBook.getAsks().isEmpty()) {
            Money lastBestAsk = lastOrderBook.getAsks().get(0).getPrice();
            Money newBestAsk = newOrderBook.getAsks().get(0).getPrice();

            if (!lastBestAsk.equals(newBestAsk)) {
                return true;
            }
        } else if (lastOrderBook.getAsks().isEmpty() != newOrderBook.getAsks().isEmpty()) {
            return true;
        }

        return false;
    }

    /**
     * Kiểm tra xem giá giao dịch cuối cùng có thay đổi không
     * @param lastOrderBook OrderBook cuối cùng
     * @param newOrderBook OrderBook mới
     * @return true nếu có thay đổi, false nếu không
     */
    private boolean hasLastPriceChanged(OrderBook lastOrderBook, OrderBook newOrderBook) {
        if (lastOrderBook.getLastPrice() != null && newOrderBook.getLastPrice() != null) {
            return !lastOrderBook.getLastPrice().equals(newOrderBook.getLastPrice());
        }

        return lastOrderBook.getLastPrice() == null != (newOrderBook.getLastPrice() == null);
    }

    /**
     * Kiểm tra xem giá đánh dấu có thay đổi không
     * @param lastOrderBook OrderBook cuối cùng
     * @param newOrderBook OrderBook mới
     * @return true nếu có thay đổi, false nếu không
     */
    private boolean hasMarkPriceChanged(OrderBook lastOrderBook, OrderBook newOrderBook) {
        if (lastOrderBook.getMarkPrice() != null && newOrderBook.getMarkPrice() != null) {
            return !lastOrderBook.getMarkPrice().equals(newOrderBook.getMarkPrice());
        }

        return lastOrderBook.getMarkPrice() == null != (newOrderBook.getMarkPrice() == null);
    }
}
