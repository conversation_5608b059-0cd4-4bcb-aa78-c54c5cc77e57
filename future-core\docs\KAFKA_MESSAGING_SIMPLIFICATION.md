# Kafka Messaging Simplification

## Tổng quan

Đã thực hiện việc đơn giản hóa luồng gửi message Kafka trong module future-core, loại bỏ intelligent sharding và order routing, chỉ sử dụng traditional order command routing.

## Thay đổi chính

### 1. OrderCommandProducer Simplification

**Trước:**
- Sử dụng intelligent sharding với `IntelligentOrderRouter`
- Có 2 topic: `order-commands` và `order-routing`
- Logic phức tạp với fallback mechanism
- Dependencies: `IntelligentOrderRouter`, `ShardingIntegrationService`

**Sau:**
- Chỉ sử dụng traditional routing
- Chỉ có 1 topic: `order-commands`
- <PERSON> đơn giản, trực tiếp gửi đến topic
- Dependencies: Chỉ `KafkaTemplate` và `ObjectMapper`

### 2. Code Changes

#### OrderCommandProducer.java
```java
// BEFORE: Complex intelligent sharding logic
if (intelligentShardingEnabled && shardingService.canProcessSymbol(order.getSymbol().getValue())) {
    // Route through intelligent sharding system
    orderRouter.routeOrder(order).thenAccept(trades -> {
        // Success handling
    }).exceptionally(throwable -> {
        // Fallback to traditional routing
        sendOrderCommand(command);
        return null;
    });
} else {
    // Traditional routing
    sendOrderCommand(command);
}

// AFTER: Simple direct routing
public void sendPlaceOrderCommand(Order order) {
    OrderCommand command = OrderCommand.builder()
            .commandId(UUID.randomUUID().toString())
            .type(OrderCommand.OrderCommandType.PLACE_ORDER)
            .order(order)
            .timestamp(LocalDateTime.now())
            .build();

    log.info("Gửi lệnh đến Kafka topic: {}, orderId: {}, symbol: {}",
            orderCommandsTopic, order.getOrderId().getValue(), order.getSymbol().getValue());

    sendOrderCommand(command);
}
```

#### Error Handling Improvement
```java
// BEFORE: Silent failure
} catch (Exception e) {
    log.error("Lỗi khi gửi command: {}, symbol: {}, topic: {}", command.getType(), symbol, targetTopic, e);
}

// AFTER: Proper exception handling
} catch (Exception e) {
    log.error("Lỗi khi gửi command: {}, symbol: {}, topic: {}", command.getType(), symbol, orderCommandsTopic, e);
    throw new MessagingException("Failed to send order command to Kafka", e);
}
```

### 3. New Exception Class

Tạo `MessagingException` để handle messaging errors:

```java
public class MessagingException extends RuntimeException {
    public MessagingException(String message) {
        super(message);
    }

    public MessagingException(String message, Throwable cause) {
        super(message, cause);
    }

    public MessagingException(Throwable cause) {
        super(cause);
    }
}
```

### 4. PlaceOrderService Update

Cập nhật error handling trong `sendOrderToKafka`:

```java
private void sendOrderToKafka(Order order) {
    try {
        orderCommandProducer.sendPlaceOrderCommand(order);
        log.info("Đã gửi lệnh đến OrderCommandProducer thành công, orderId = {}", order.getOrderId().getValue());
    } catch (Exception e) {
        log.error("Gửi lệnh đến OrderCommandProducer thất bại, orderId = {}", order.getOrderId().getValue(), e);
        // Không throw exception để không làm fail toàn bộ transaction đặt lệnh
        // Order đã được lưu thành công, chỉ việc gửi Kafka bị lỗi
    }
}
```

## Lợi ích

### 1. Đơn giản hóa
- Loại bỏ complexity không cần thiết
- Dễ debug và maintain
- Ít dependencies

### 2. Reliability
- Proper error handling với dedicated exception
- Clear logging cho debugging
- Fail-fast behavior

### 3. Performance
- Ít overhead từ intelligent sharding logic
- Direct routing nhanh hơn
- Ít memory usage

## Configuration

Topic configuration vẫn giữ nguyên trong `application.yaml`:

```yaml
topic-kafka:
  contract:
    order-commands: order-commands
    order-events: order-events
```

## Testing

Đã tạo unit test cho `OrderCommandProducer` để đảm bảo:
- Success case
- JSON processing exception
- Kafka send exception

## Migration Notes

1. **Backward Compatibility**: Vẫn tương thích với existing consumers
2. **Topic Usage**: Chỉ sử dụng `order-commands` topic
3. **Error Handling**: Improved với proper exception types
4. **Logging**: Enhanced logging cho better debugging

## Troubleshooting

### Common Issues

1. **Topic không tồn tại**: Kiểm tra Kafka topic configuration
2. **Serialization error**: Kiểm tra ObjectMapper configuration
3. **Connection issues**: Kiểm tra Kafka bootstrap servers

### Debug Steps

1. Check logs cho `OrderCommandProducer`
2. Verify topic configuration trong application.yaml
3. Test Kafka connectivity
4. Monitor Kafka consumer logs

## Future Improvements

1. Add retry mechanism với exponential backoff
2. Add metrics cho monitoring
3. Add circuit breaker pattern
4. Consider async processing với CompletableFuture
