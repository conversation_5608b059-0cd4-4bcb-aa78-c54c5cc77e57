package com.icetea.lotus.infrastructure.messaging.producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.exception.MessagingException;
import com.icetea.lotus.infrastructure.messaging.command.OrderCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Producer cho OrderCommand - Traditional routing only
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCommandProducer {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    @Value("${topic-kafka.contract.order-commands}")
    private String orderCommandsTopic;

    /**
     * <PERSON><PERSON><PERSON> command đặt lệnh
     *
     * @param order Lệnh
     */
    public void sendPlaceOrderCommand(Order order) {
        OrderCommand command = OrderCommand.builder()
                .commandId(UUID.randomUUID().toString())
                .type(OrderCommand.OrderCommandType.PLACE_ORDER)
                .order(order)
                .timestamp(LocalDateTime.now())
                .build();

        log.info("Gửi lệnh đến Kafka topic: {}, orderId: {}, symbol: {}",
                orderCommandsTopic, order.getOrderId().getValue(), order.getSymbol().getValue());

        // Gửi command trực tiếp đến topic order-commands
        sendOrderCommand(command);
    }

    /**
     * Gửi command hủy lệnh
     *
     * @param order Lệnh
     */
    public void sendCancelOrderCommand(Order order) {
        OrderCommand command = OrderCommand.builder()
                .commandId(UUID.randomUUID().toString())
                .type(OrderCommand.OrderCommandType.CANCEL_ORDER)
                .order(order)
                .timestamp(LocalDateTime.now())
                .build();

        sendOrderCommand(command);
    }

    /**
     * Gửi command cập nhật lệnh
     *
     * @param order Lệnh
     */
    public void sendUpdateOrderCommand(Order order) {
        OrderCommand command = OrderCommand.builder()
                .commandId(UUID.randomUUID().toString())
                .type(OrderCommand.OrderCommandType.UPDATE_ORDER)
                .order(order)
                .timestamp(LocalDateTime.now())
                .build();

        sendOrderCommand(command);
    }

    /**
     * Gửi OrderCommand đến topic order-commands
     *
     * @param command OrderCommand
     */
    private void sendOrderCommand(OrderCommand command) {
        String symbol = command.getOrder().getSymbol().getValue();

        try {
            kafkaTemplate.send(orderCommandsTopic, symbol, objectMapper.writeValueAsString(command));
            log.info("Đã gửi command: {}, symbol: {}, topic: {}", command.getType(), symbol, orderCommandsTopic);
        } catch (Exception e) {
            log.error("Lỗi khi gửi command: {}, symbol: {}, topic: {}", command.getType(), symbol, orderCommandsTopic, e);
            // Rethrow exception để caller có thể handle
            throw new MessagingException("Failed to send order command to Kafka", e);
        }
    }
}
