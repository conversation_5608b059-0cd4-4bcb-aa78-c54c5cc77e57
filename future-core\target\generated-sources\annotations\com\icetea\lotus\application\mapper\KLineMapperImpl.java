package com.icetea.lotus.application.mapper;

import com.icetea.lotus.application.dto.KLineDto;
import com.icetea.lotus.core.domain.entity.KLine;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-09T21:58:43+0700",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class KLineMapperImpl implements KLineMapper {

    @Override
    public KLineDto toDto(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }

        KLineDto.KLineDtoBuilder kLineDto = KLineDto.builder();

        kLineDto.symbol( kLineSymbolValue( kLine ) );
        kLineDto.openPrice( kLineOpenPriceValue( kLine ) );
        kLineDto.highestPrice( kLineHighestPriceValue( kLine ) );
        kLineDto.lowestPrice( kLineLowestPriceValue( kLine ) );
        kLineDto.closePrice( kLineClosePriceValue( kLine ) );
        kLineDto.count( kLine.getCount() );
        kLineDto.createTime( kLine.getCreateTime() );
        kLineDto.period( kLine.getPeriod() );
        kLineDto.time( kLine.getTime() );
        kLineDto.turnover( kLine.getTurnover() );
        kLineDto.updateTime( kLine.getUpdateTime() );
        kLineDto.volume( kLine.getVolume() );

        return kLineDto.build();
    }

    @Override
    public List<KLineDto> toDtoList(List<KLine> kLines) {
        if ( kLines == null ) {
            return null;
        }

        List<KLineDto> list = new ArrayList<KLineDto>( kLines.size() );
        for ( KLine kLine : kLines ) {
            list.add( toDto( kLine ) );
        }

        return list;
    }

    private String kLineSymbolValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Symbol symbol = kLine.getSymbol();
        if ( symbol == null ) {
            return null;
        }
        String value = symbol.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }

    private BigDecimal kLineOpenPriceValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Money openPrice = kLine.getOpenPrice();
        if ( openPrice == null ) {
            return null;
        }
        BigDecimal value = openPrice.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }

    private BigDecimal kLineHighestPriceValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Money highestPrice = kLine.getHighestPrice();
        if ( highestPrice == null ) {
            return null;
        }
        BigDecimal value = highestPrice.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }

    private BigDecimal kLineLowestPriceValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Money lowestPrice = kLine.getLowestPrice();
        if ( lowestPrice == null ) {
            return null;
        }
        BigDecimal value = lowestPrice.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }

    private BigDecimal kLineClosePriceValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Money closePrice = kLine.getClosePrice();
        if ( closePrice == null ) {
            return null;
        }
        BigDecimal value = closePrice.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }
}
